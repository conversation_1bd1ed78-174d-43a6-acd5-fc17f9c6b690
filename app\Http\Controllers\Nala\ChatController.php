<?php

namespace App\Http\Controllers\Nala;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use App\Models\NalaChatConversation;
use App\Models\NalaChatMessage;
use App\Http\Controllers\Nala\UserProfileController;
use App\Http\Controllers\Nala\CareerController;
use App\Http\Controllers\Nala\CourseEngineController;
use App\Http\Controllers\Nala\ExamController;
use App\Http\Controllers\Nala\TutorController;

class ChatController extends Controller
{
    private $geminiApiKey;
    private $geminiModel;

    public function __construct()
    {
        $this->geminiApiKey = config('services.gemini.api_key', env('GEMINI_API_KEY'));
        $this->geminiModel = config('services.gemini.model', env('GEMINI_MODEL', 'gemini-2.0-flash'));
    }

    /**
     * Test endpoint for debugging new responses
     */
    public function testChat(Request $request)
    {
        $message = $request->input('message', 'halo nala');

        // Force new conversation for testing
        $conversation = NalaChatConversation::create([
            'id' => Str::uuid(),
            'user_id' => auth()->id(),
            'started_route' => 'test',
            'started_context' => 'test'
        ]);

        $userProfile = ['basic_info' => ['name' => 'Test User']];
        $response = $this->generateGeneralResponse($message, 'test', $userProfile, $conversation);

        return response()->json([
            'success' => true,
            'response' => $response,
            'message' => $message,
            'debug' => 'This is a fresh response with new system prompt'
        ]);
    }

    /**
     * Handle NALA AI chat requests
     */
    public function chat(Request $request)
    {
        $request->validate([
            'message' => 'required|string|max:1000',
            'context' => 'nullable|array',
            'current_route' => 'nullable|string',
            'current_context' => 'nullable|string',
            'conversation_id' => 'nullable|string|exists:nala_chat_conversations,id',
            'tutor_context' => 'nullable|array'
        ]);

        try {
            $userMessage = $request->input('message');
            $currentRoute = $request->input('current_route');
            $currentContext = $request->input('current_context');
            $conversationId = $request->input('conversation_id');

            // Get or create conversation
            $conversation = $this->getOrCreateConversation($conversationId, $currentRoute, $currentContext);

            // Store user message
            $this->storeMessage($conversation, 'user', $userMessage);

            // Check for prohibited content first
            $prohibitionCheck = $this->checkProhibitedContent($userMessage);
            if ($prohibitionCheck) {
                $this->storeMessage($conversation, 'ai', $prohibitionCheck, ['is_prohibited' => true]);
                return response()->json([
                    'success' => true,
                    'response' => $prohibitionCheck,
                    'prohibited' => true,
                    'conversation_id' => $conversation->id
                ]);
            }

            // Get user profile and membership
            $userProfile = null;
            $userMembership = 'free';
            if (auth()->check()) {
                $user = auth()->user();
                $userProfile = app(UserProfileController::class)->buildUserProfile($user);
                $userMembership = $this->getUserMembershipLevel($user);
            }

            // Check message limits
            if (!$this->canUserSendMessage($userMembership)) {
                $limitResponse = $this->getMembershipLimitResponse($userMembership);
                $this->storeMessage($conversation, 'ai', $limitResponse, ['is_limit_reached' => true]);
                return response()->json([
                    'success' => true,
                    'response' => $limitResponse,
                    'limit_reached' => true,
                    'conversation_id' => $conversation->id
                ]);
            }

            // Route to appropriate controller based on context
            $response = $this->routeToSpecializedController($userMessage, $currentContext, $userProfile, $userMembership, $request);

            // If no specialized response, use general AI
            if (!$response) {
                $response = $this->generateGeneralResponse($userMessage, $currentContext, $userProfile, $conversation, $request);
            }

            // Store AI response
            $this->storeMessage($conversation, 'ai', $response);

            // Increment message count
            if (auth()->check()) {
                $this->incrementMessageCount(auth()->id());
            }

            return response()->json([
                'success' => true,
                'response' => $response,
                'conversation_id' => $conversation->id
            ]);

        } catch (\Exception $e) {
            Log::error('Nala Chat Error: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'response' => 'Maaf, saya sedang mengalami gangguan. Silakan coba lagi dalam beberapa saat.',
                'error' => app()->environment('local') ? $e->getMessage() : null
            ], 500);
        }
    }

    /**
     * Route message to specialized controller
     */
    private function routeToSpecializedController($message, $context, $userProfile, $membership, $request = null)
    {
        $lowerMessage = strtolower($message);

        // Skip routing for basic greetings and simple messages
        if ($this->isBasicGreeting($lowerMessage)) {
            return null; // Handle with general response
        }

        // Only route if message is clearly asking for specific domain help
        if ($this->isSpecificCareerQuestion($lowerMessage)) {
            return app(CareerController::class)->handleCareerQuestion($message, $context, $userProfile, $membership);
        }

        if ($this->isSpecificCourseQuestion($lowerMessage, $context)) {
            // Pass course context if available
            $courseContext = $request ? $request->input('course_context', []) : [];
            return app(CourseEngineController::class)->handleCourseQuestion($message, $context, $userProfile, $membership, $courseContext);
        }

        if ($this->isSpecificExamQuestion($lowerMessage, $context)) {
            return app(ExamController::class)->handleExamQuestion($message, $context, $userProfile, $membership);
        }

        if ($this->isSpecificTutorQuestion($lowerMessage, $context)) {
            // Pass tutor context if available
            $tutorContext = $request ? $request->input('tutor_context', []) : [];

            // Enhanced debug logging
            Log::info('Nala ChatController: Routing to TutorController', [
                'message' => $message,
                'context' => $context,
                'tutor_context_keys' => array_keys($tutorContext),
                'tutor_context_full' => $tutorContext,
                'courses_count' => count($tutorContext['courses'] ?? []),
                'exams_count' => count($tutorContext['exams'] ?? []),
                'blogs_count' => count($tutorContext['blogs'] ?? []),
                'tutor_info' => $tutorContext['tutor'] ?? null,
                'stats_info' => $tutorContext['stats'] ?? null,
                'request_all_data' => $request ? $request->all() : null
            ]);

            return app(TutorController::class)->handleTutorQuestion($message, $context, $userProfile, $membership, $tutorContext);
        }

        return null; // Handle with general response
    }

    /**
     * Generate general AI response
     */
    private function generateGeneralResponse($message, $context, $userProfile, $conversation, $request = null)
    {
        // Build route-specific system prompt with course context if available
        $systemPrompt = $this->buildRouteSpecificSystemPrompt($context, $userProfile, $conversation, $request);

        // Get conversation history (limited)
        $conversationHistory = $this->getConversationHistory($conversation, 5); // Only last 5 messages

        // Build contextual prompt
        $contextualPrompt = $this->buildContextualPrompt($message, $context, $conversationHistory);

        try {
            return $this->callGeminiAPI($systemPrompt, $contextualPrompt);
        } catch (\Exception $e) {
            Log::error('Gemini API Error: ' . $e->getMessage());
            return $this->getFallbackResponse($context);
        }
    }

    /**
     * Build route-specific system prompt
     */
    private function buildRouteSpecificSystemPrompt($context, $userProfile, $conversation, $request = null)
    {
        $name = $userProfile['basic_info']['name'] ?? 'Pengguna';
        $route = $conversation->started_route ?? 'unknown';

        // Get route-specific context
        $routeContext = $this->getRouteSpecificContext($route, $context);

        // Add course context if available
        $courseContextInfo = '';
        if ($request && $request->input('course_context.course')) {
            $courseData = $request->input('course_context.course');
            $courseContextInfo = "\n\nKONTEKS KURSUS SAAT INI:";
            $courseContextInfo .= "\n- Judul: " . ($courseData['title'] ?? 'Tidak diketahui');
            $courseContextInfo .= "\n- Level: " . ($courseData['level'] ?? 'Tidak diketahui');
            $courseContextInfo .= "\n- Harga: " . ($courseData['price'] ?? 'Tidak diketahui');
            $courseContextInfo .= "\n- Deskripsi: " . ($courseData['description'] ?? 'Tidak diketahui');
            if (isset($courseData['tutor'])) {
                $courseContextInfo .= "\n- Tutor: " . $courseData['tutor'];
            }
            $courseContextInfo .= "\n\nGunakan informasi ini untuk memberikan respons yang lebih personal dan relevan tentang kursus ini.";
        }

        // Add tutor context if available
        $tutorContextInfo = '';
        if ($request && $request->input('tutor_context.tutor')) {
            $tutorData = $request->input('tutor_context.tutor');
            $tutorStats = $request->input('tutor_context.stats', []);
            $tutorCourses = $request->input('tutor_context.courses', []);
            $tutorExams = $request->input('tutor_context.exams', []);

            $tutorContextInfo = "\n\nKONTEKS TUTOR SAAT INI:";
            $tutorContextInfo .= "\n- Nama: " . ($tutorData['name'] ?? 'Tidak diketahui');
            $tutorContextInfo .= "\n- Posisi: " . ($tutorData['job_title'] ?? 'Tidak diketahui');
            $tutorContextInfo .= "\n- Perusahaan: " . ($tutorData['company'] ?? 'Tidak diketahui');
            $tutorContextInfo .= "\n- Lokasi: " . ($tutorData['location'] ?? 'Tidak diketahui');
            $tutorContextInfo .= "\n- Pendidikan: " . ($tutorData['education_level'] ?? 'Tidak diketahui');
            $tutorContextInfo .= "\n- Bergabung: " . ($tutorData['joined_date'] ?? 'Tidak diketahui');

            if (!empty($tutorData['skills'])) {
                $tutorContextInfo .= "\n- Keahlian: " . implode(', ', $tutorData['skills']);
            }

            if (!empty($tutorData['description'])) {
                $tutorContextInfo .= "\n- Deskripsi: " . $tutorData['description'];
            }

            $tutorContextInfo .= "\n\nSTATISTIK TUTOR:";
            $tutorContextInfo .= "\n- Total Kursus: " . ($tutorStats['total_courses'] ?? 0);
            $tutorContextInfo .= "\n- Total Ujian: " . ($tutorStats['total_exams'] ?? 0);
            $tutorContextInfo .= "\n- Total Siswa: " . ($tutorStats['total_students'] ?? 0);
            $tutorContextInfo .= "\n- Rating Rata-rata: " . ($tutorStats['average_rating'] ?? 0);

            if (!empty($tutorCourses)) {
                $tutorContextInfo .= "\n\nKURSUS TUTOR:";
                foreach (array_slice($tutorCourses, 0, 3) as $course) {
                    $tutorContextInfo .= "\n- " . ($course['title'] ?? 'Tidak diketahui') . " (" . ($course['level'] ?? 'Tidak diketahui') . ")";
                }
            }

            $tutorContextInfo .= "\n\nGunakan informasi ini untuk memberikan respons yang personal dan relevan tentang tutor ini. Bantu user memahami keahlian dan pengalaman tutor.";
        }

        $basePrompt = "Anda adalah Nala, asisten belajar yang ramah di Ngambiskuy.

Karakteristik:
- Ramah, natural, dan supportif
- Berbicara seperti teman yang membantu belajar
- Jawaban praktis dan mudah dipahami
- Maksimal 100 kata untuk respons singkat

{$routeContext}{$courseContextInfo}{$tutorContextInfo}

KONTEKS PLATFORM NGAMBISKUY:
Ngambiskuy adalah platform edukasi teknologi yang fokus pada:
- Programming (Python, JavaScript, PHP, Java, React, Vue, Laravel)
- Web Development (Frontend, Backend, Full Stack)
- Mobile Development (Android, iOS, React Native, Flutter)
- Data Science (Machine Learning, AI, Data Analysis)
- UI/UX Design (Design Thinking, Prototyping, User Research)
- Digital Marketing (SEO, Social Media, Content Marketing)
- Business (Entrepreneurship, Project Management, Leadership)
- Cybersecurity (Network Security, Ethical Hacking)

SANGAT PENTING - LARANGAN MUTLAK:
- JANGAN PERNAH sebutkan 'AI Assistant Ngambiskuy'
- JANGAN PERNAH sebutkan 'rekomendasi kursus, analisis jalur karir, dan panduan pembelajaran'
- JANGAN PERNAH sebutkan sistem teknis (ICE, database, analisis profil, dll)
- JANGAN gunakan istilah teknis yang membingungkan
- JANGAN bicara formal atau kaku
- Bicara natural seperti teman biasa
- JANGAN mengklaim kemitraan palsu atau data yang tidak akurat
- JANGAN PERNAH merekomendasikan kursus di luar kategori teknologi (seperti masak, olahraga, musik, dll)
- HANYA rekomendasikan kursus yang sesuai dengan kategori platform teknologi

Untuk greeting sederhana: jawab singkat dan ramah (maksimal 20 kata)
Contoh: 'Halo! Ada yang bisa saya bantu dengan belajar hari ini?'

Pengguna: {$name}
Situasi: {$context}
Route: {$route}

Jawab dengan natural dan ramah seperti teman.";

        return $basePrompt;
    }

    /**
     * Get route-specific context for system prompt
     */
    private function getRouteSpecificContext($route, $context)
    {
        $routeContexts = [
            // Public routes (matching actual Laravel route names)
            'home' => 'KONTEKS: Halaman utama Ngambiskuy. Bantu user memahami platform dan mulai belajar.',
            'courses.index' => 'KONTEKS: Halaman daftar kursus. Bantu user menemukan kursus yang sesuai minat.',
            'exams.index' => 'KONTEKS: Halaman daftar ujian/tryout. Bantu user memilih ujian untuk sertifikasi.',
            'exams.show' => 'KONTEKS: Detail ujian. Jelaskan tentang ujian ini dan cara persiapannya.',
            'exams.take' => 'KONTEKS: User sedang mengerjakan ujian. JANGAN berikan jawaban ujian!',
            'exams.result' => 'KONTEKS: Hasil ujian. Berikan motivasi dan saran untuk improvement.',
            'blog.index' => 'KONTEKS: Halaman blog. Bantu user temukan artikel pembelajaran.',
            'blog.show' => 'KONTEKS: Detail artikel blog. Diskusi konten artikel dan topik terkait.',
            'blog.category' => 'KONTEKS: Kategori blog. Bantu user eksplorasi artikel dalam kategori ini.',
            'course.show' => 'KONTEKS: Detail kursus. Jelaskan manfaat dan isi kursus ini.',
            'course.learn' => 'KONTEKS: User sedang belajar. Bantu jelaskan materi yang sulit.',
            'tutor.public-profile' => 'KONTEKS: Profil tutor. Jelaskan keahlian dan pengalaman tutor.',
            'payment.pricing' => 'KONTEKS: Halaman pricing. Jelaskan manfaat setiap paket membership.',

            // User Dashboard routes
            'user.dashboard' => 'KONTEKS: Dashboard user. Bantu user navigasi dan memahami progress.',
            'user.profile' => 'KONTEKS: Profil user. Bantu user melengkapi dan update profil.',
            'user.courses' => 'KONTEKS: Kursus user. Bantu user manage kursus yang diambil.',
            'user.exams' => 'KONTEKS: Ujian user. Bantu user lihat hasil dan jadwal ujian.',
            'user.blog' => 'KONTEKS: Blog user. Bantu user manage artikel yang disimpan.',
            'user.progress' => 'KONTEKS: Progress belajar. Motivasi user untuk terus belajar.',
            'user.certificates' => 'KONTEKS: Sertifikat user. Bantu user download dan share sertifikat.',
            'user.settings' => 'KONTEKS: Pengaturan user. Bantu user konfigurasi akun.',
            'user.membership' => 'KONTEKS: Membership user. Jelaskan benefit upgrade membership.',

            // Tutor Dashboard routes (matching actual web.php routes)
            'tutor.dashboard' => 'KONTEKS: Dashboard tutor. Bantu tutor manage konten dan siswa.',
            'tutor.courses' => 'KONTEKS: Kursus tutor. Bantu tutor manage kursus yang dibuat.',
            'tutor.create-course' => 'KONTEKS: Buat kursus baru. Bantu tutor dengan tips membuat kursus berkualitas.',
            'tutor.edit-course' => 'KONTEKS: Edit kursus. Bantu tutor improve kursus yang ada.',
            'tutor.curriculum.index' => 'KONTEKS: Kurikulum kursus. Bantu tutor struktur pembelajaran.',
            'tutor.curriculum.create-material' => 'KONTEKS: Buat materi. Tips membuat konten engaging.',
            'tutor.curriculum.edit-material' => 'KONTEKS: Edit materi. Bantu improve konten existing.',
            'tutor.exams' => 'KONTEKS: Ujian tutor. Bantu tutor manage ujian yang dibuat.',
            'tutor.exams.create' => 'KONTEKS: Buat ujian. Tips membuat soal berkualitas.',
            'tutor.exams.edit' => 'KONTEKS: Edit ujian. Bantu improve soal dan penilaian.',
            'tutor.exams.show' => 'KONTEKS: Detail ujian tutor. Bantu tutor analisis performa ujian.',
            'tutor.blogs' => 'KONTEKS: Blog tutor. Bantu tutor manage artikel yang dibuat.',
            'tutor.blogs.create' => 'KONTEKS: Buat artikel blog. Tips menulis konten menarik.',
            'tutor.blogs.edit' => 'KONTEKS: Edit artikel blog. Bantu improve konten artikel.',
            'tutor.blogs.show' => 'KONTEKS: Detail artikel tutor. Bantu analisis performa artikel.',
            'tutor.students' => 'KONTEKS: Siswa tutor. Bantu tutor engage dengan siswa.',
            'tutor.analytics' => 'KONTEKS: Analytics tutor. Bantu tutor pahami performa konten.',
            'tutor.earnings' => 'KONTEKS: Penghasilan tutor. Bantu tutor optimasi income.',
            'tutor.profile' => 'KONTEKS: Profil tutor. Bantu tutor buat profil menarik.',
            'tutor.settings' => 'KONTEKS: Pengaturan tutor. Bantu tutor konfigurasi akun.',

            // Tutor Registration routes
            'tutor.register.terms' => 'KONTEKS: Syarat jadi tutor. Jelaskan proses dan requirement.',
            'tutor.register.profile' => 'KONTEKS: Daftar tutor. Bantu lengkapi profil tutor.',
            'tutor.register.review' => 'KONTEKS: Review aplikasi tutor. Jelaskan proses review.',
            'tutor.register.status' => 'KONTEKS: Status aplikasi tutor. Update status dan next steps.',

            // Tutor Public Profile
            'tutor.public-profile' => 'KONTEKS: Profil publik tutor. Bantu user dengan informasi tentang tutor ini.',

            // Payment routes
            'payment.membership.checkout' => 'KONTEKS: Checkout membership. Bantu user pilih paket yang tepat.',
            'payment.course.checkout' => 'KONTEKS: Checkout kursus. Konfirmasi pilihan dan benefit kursus.'
        ];

        return $routeContexts[$route] ?? 'KONTEKS: Halaman umum Ngambiskuy. Bantu user dengan pertanyaan pembelajaran.';
    }

    /**
     * Build simplified system prompt (fallback)
     */
    private function buildSimpleSystemPrompt($context, $userProfile)
    {
        $name = $userProfile['basic_info']['name'] ?? 'Pengguna';

        $prompt = "Anda adalah Nala, asisten belajar yang ramah di Ngambiskuy.

Karakteristik:
- Ramah, natural, dan supportif
- Berbicara seperti teman yang membantu belajar
- Jawaban praktis dan mudah dipahami
- Maksimal 100 kata untuk respons singkat

KONTEKS PLATFORM NGAMBISKUY:
Ngambiskuy adalah platform edukasi teknologi yang fokus pada:
- Programming (Python, JavaScript, PHP, Java, React, Vue, Laravel)
- Web Development (Frontend, Backend, Full Stack)
- Mobile Development (Android, iOS, React Native, Flutter)
- Data Science (Machine Learning, AI, Data Analysis)
- UI/UX Design (Design Thinking, Prototyping, User Research)
- Digital Marketing (SEO, Social Media, Content Marketing)
- Business (Entrepreneurship, Project Management, Leadership)
- Cybersecurity (Network Security, Ethical Hacking)

SANGAT PENTING - LARANGAN MUTLAK:
- JANGAN PERNAH sebutkan 'AI Assistant Ngambiskuy'
- JANGAN PERNAH sebutkan 'rekomendasi kursus, analisis jalur karir, dan panduan pembelajaran'
- JANGAN PERNAH sebutkan sistem teknis (ICE, database, analisis profil, dll)
- JANGAN gunakan istilah teknis yang membingungkan
- JANGAN bicara formal atau kaku
- Bicara natural seperti teman biasa
- JANGAN PERNAH merekomendasikan kursus di luar kategori teknologi (seperti masak, olahraga, musik, dll)
- HANYA rekomendasikan kursus yang sesuai dengan kategori platform teknologi

Untuk greeting sederhana: jawab singkat dan ramah (maksimal 20 kata)
Contoh: 'Halo! Ada yang bisa saya bantu dengan belajar hari ini?'

Pengguna: {$name}
Situasi: {$context}

Jawab dengan natural dan ramah seperti teman.";

        return $prompt;
    }

    /**
     * Build contextual prompt
     */
    private function buildContextualPrompt($message, $context, $history)
    {
        // Handle basic greetings differently
        if ($this->isBasicGreeting(strtolower($message))) {
            return "Pesan: {$message}\n\nIni adalah sapaan sederhana. Jawab dengan ramah dan singkat (maksimal 30 kata). Jangan berlebihan atau terlalu panjang.";
        }

        $prompt = "Situasi: {$context}\n\n";

        if (!empty($history) && count($history) > 0) {
            $prompt .= "Percakapan sebelumnya:\n";
            foreach (array_slice($history, -3) as $msg) { // Only last 3 messages
                $sender = $msg['sender'] === 'user' ? 'User' : 'Nala';
                $prompt .= "{$sender}: " . substr($msg['content'], 0, 80) . "\n";
            }
            $prompt .= "\n";
        }

        $prompt .= "Pesan: {$message}\n\n";
        $prompt .= "Jawab dengan natural dan helpful. Maksimal 100 kata.";

        return $prompt;
    }

    /**
     * Check if message is a basic greeting
     */
    private function isBasicGreeting($message)
    {
        $greetings = [
            'halo', 'hai', 'hello', 'hi', 'selamat pagi', 'selamat siang', 'selamat sore', 'selamat malam',
            'good morning', 'good afternoon', 'good evening', 'good night',
            'apa kabar', 'how are you', 'bagaimana kabar', 'apa saja', 'apa aja',
            'terima kasih', 'thank you', 'thanks', 'makasih', 'ok', 'oke', 'baik',
            'baru saja', 'test', 'testing', 'coba', 'tes'
        ];

        $message = trim($message);

        // Check if message is very short (likely greeting)
        if (strlen($message) <= 15) {
            foreach ($greetings as $greeting) {
                if (strpos($message, $greeting) !== false) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * Check if message is specifically asking about career (not just mentioning it)
     */
    private function isSpecificCareerQuestion($message)
    {
        $specificCareerQuestions = [
            'analisis karir', 'jalur karir', 'career path', 'roadmap karir',
            'gaji berapa', 'salary range', 'penghasilan', 'prospek karir',
            'skill apa yang dibutuhkan', 'kemampuan apa', 'skill gap',
            'industri apa', 'tren industri', 'peluang kerja'
        ];

        foreach ($specificCareerQuestions as $question) {
            if (strpos($message, $question) !== false) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if message is specifically asking about courses
     */
    private function isSpecificCourseQuestion($message, $context)
    {
        $specificCourseQuestions = [
            'rekomendasi kursus', 'kursus apa', 'course recommendation',
            'belajar apa', 'materi apa', 'pembelajaran apa',
            'jalur belajar', 'learning path', 'roadmap belajar'
        ];

        // Strong course contexts
        $strongCourseContexts = ['course_detail', 'course_listing', 'learning_page'];

        foreach ($specificCourseQuestions as $question) {
            if (strpos($message, $question) !== false) {
                return true;
            }
        }

        return in_array($context, $strongCourseContexts);
    }

    /**
     * Check if message is specifically asking about exams
     */
    private function isSpecificExamQuestion($message, $context)
    {
        $specificExamQuestions = [
            'ujian apa', 'exam apa', 'sertifikat apa', 'certification',
            'persiapan ujian', 'tips ujian', 'cara lulus',
            'sertifikasi apa', 'certificate'
        ];

        // Strong exam contexts
        $strongExamContexts = ['exam_detail', 'exam_listing', 'exam_taking'];

        foreach ($specificExamQuestions as $question) {
            if (strpos($message, $question) !== false) {
                return true;
            }
        }

        return in_array($context, $strongExamContexts);
    }

    /**
     * Check if message is specifically asking about tutors
     */
    private function isSpecificTutorQuestion($message, $context)
    {
        $specificTutorQuestions = [
            'tentang tutor', 'profil tutor', 'siapa tutor', 'tutor ini',
            'pengalaman tutor', 'keahlian tutor', 'background tutor',
            'kursus tutor', 'tutor mengajar', 'cara mengajar',
            'rating tutor', 'review tutor', 'testimoni tutor'
        ];

        // Strong tutor contexts
        $strongTutorContexts = ['tutor_public_profile'];

        foreach ($specificTutorQuestions as $question) {
            if (strpos($message, $question) !== false) {
                return true;
            }
        }

        return in_array($context, $strongTutorContexts);
    }

    /**
     * Check for prohibited content
     */
    private function checkProhibitedContent($message)
    {
        $lowerMessage = strtolower($message);
        
        $prohibitions = [
            'bad_language' => [
                'keywords' => ['anjing', 'babi', 'bangsat', 'fuck', 'shit', 'bodoh', 'tolol', 'goblok'],
                'response' => 'Maaf, mari kita jaga percakapan tetap sopan dan fokus pada pembelajaran! Bagaimana kalau kita bahas topik yang berkaitan dengan kursus atau karir Anda?'
            ],
            'religion' => [
                'keywords' => ['agama', 'islam', 'kristen', 'hindu', 'buddha', 'doa', 'tuhan', 'allah'],
                'response' => 'Saya fokus pada topik edukasi dan tidak membahas masalah agama. Mari kita jelajahi skill yang ingin Anda pelajari atau jalur karir yang Anda minati!'
            ],
            'politics' => [
                'keywords' => ['politik', 'partai', 'pemilu', 'jokowi', 'prabowo', 'pemerintah', 'presiden'],
                'response' => 'Saya tidak terlibat dalam diskusi politik. Mari kita bicarakan sesuatu yang lebih selaras dengan perjalanan belajar Anda!'
            ],
            'competitors' => [
                'keywords' => ['ruangguru', 'zenius', 'udemy', 'coursera', 'skillshare'],
                'response' => 'Mari kita fokus pada apa yang bisa Ngambiskuy tawarkan untuk membantu Anda berkembang. Apa yang ingin Anda pelajari?'
            ]
        ];

        foreach ($prohibitions as $data) {
            foreach ($data['keywords'] as $keyword) {
                if (strpos($lowerMessage, $keyword) !== false) {
                    return $data['response'];
                }
            }
        }

        return null;
    }

    /**
     * Get fallback response when AI fails
     */
    private function getFallbackResponse($context)
    {
        $responses = [
            'course_detail' => 'Saya bisa bantu jelaskan tentang kursus ini. Ada yang ingin ditanyakan?',
            'learning_page' => 'Saya siap bantu kalau ada materi yang kurang jelas!',
            'exam_detail' => 'Butuh tips untuk ujian ini? Tanya aja!',
            'default' => 'Halo! Saya Nala, siap bantu dengan pertanyaan belajar Anda. Ada yang bisa dibantu?'
        ];

        return $responses[$context] ?? $responses['default'];
    }

    /**
     * Call Gemini AI API
     */
    private function callGeminiAPI($systemPrompt, $userPrompt)
    {
        if (!$this->geminiApiKey) {
            throw new \Exception('Gemini API key not configured');
        }

        $url = "https://generativelanguage.googleapis.com/v1beta/models/{$this->geminiModel}:generateContent?key={$this->geminiApiKey}";

        $payload = [
            'contents' => [
                [
                    'parts' => [
                        ['text' => $systemPrompt . "\n\n" . $userPrompt]
                    ]
                ]
            ],
            'generationConfig' => [
                'temperature' => 0.7,
                'topK' => 40,
                'topP' => 0.95,
                'maxOutputTokens' => 512, // Reduced for faster responses
            ]
        ];

        $response = Http::timeout(15)->post($url, $payload); // Reduced timeout

        if (!$response->successful()) {
            throw new \Exception('Gemini API request failed: ' . $response->body());
        }

        $data = $response->json();

        if (!isset($data['candidates'][0]['content']['parts'][0]['text'])) {
            throw new \Exception('Invalid response format from Gemini API');
        }

        return trim($data['candidates'][0]['content']['parts'][0]['text']);
    }

    /**
     * Get or create conversation
     */
    private function getOrCreateConversation($conversationId, $route, $context)
    {
        if ($conversationId) {
            $conversation = NalaChatConversation::find($conversationId);
            if ($conversation && (!auth()->check() || $conversation->user_id === auth()->id())) {
                return $conversation;
            }
        }

        return NalaChatConversation::create([
            'id' => Str::uuid(),
            'user_id' => auth()->id(),
            'started_route' => $route,
            'started_context' => $context
        ]);
    }

    /**
     * Store message in database
     */
    private function storeMessage($conversation, $sender, $content, $metadata = [])
    {
        return NalaChatMessage::create([
            'conversation_id' => $conversation->id,
            'sender' => $sender,
            'content' => $content,
            'metadata' => $metadata
        ]);
    }

    /**
     * Get conversation history
     */
    private function getConversationHistory($conversation, $limit = 5)
    {
        return $conversation->messages()
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get(['sender', 'content'])
            ->reverse()
            ->toArray();
    }

    /**
     * Get user membership level
     */
    private function getUserMembershipLevel($user)
    {
        if (!$user || !$user->current_membership_id) {
            return 'free';
        }

        // Get membership plan slug from the relationship
        $membershipPlan = \App\Models\MembershipPlan::find($user->current_membership_id);
        return $membershipPlan ? $membershipPlan->slug : 'free';
    }

    /**
     * Check if user can send message based on limits
     */
    private function canUserSendMessage($membershipLevel)
    {
        if (!auth()->check()) {
            return true; // Allow guest users
        }

        $limits = [
            'free' => 10,
            'basic' => 100,
            'standard' => 300,
            'pro' => 400
        ];

        $limit = $limits[$membershipLevel] ?? 10;
        $today = now()->format('Y-m-d');

        $todayCount = DB::table('nala_chat_messages')
            ->join('nala_chat_conversations', 'nala_chat_messages.conversation_id', '=', 'nala_chat_conversations.id')
            ->where('nala_chat_conversations.user_id', auth()->id())
            ->where('nala_chat_messages.sender', 'user')
            ->whereDate('nala_chat_messages.created_at', $today)
            ->count();

        return $todayCount < $limit;
    }

    /**
     * Get membership limit response
     */
    private function getMembershipLimitResponse($membershipLevel)
    {
        $responses = [
            'free' => 'Anda telah mencapai batas 10 pesan harian untuk akun gratis. Upgrade ke membership Basic untuk mendapatkan 100 pesan per hari!',
            'basic' => 'Anda telah mencapai batas 100 pesan harian. Upgrade ke Standard untuk mendapatkan 300 pesan per hari!',
            'standard' => 'Anda telah mencapai batas 300 pesan harian. Upgrade ke Pro untuk mendapatkan 400 pesan per hari!',
            'pro' => 'Anda telah mencapai batas 400 pesan harian. Silakan coba lagi besok!'
        ];

        return $responses[$membershipLevel] ?? $responses['free'];
    }

    /**
     * Increment user message count
     */
    private function incrementMessageCount($userId)
    {
        // This could be implemented with a daily counter table if needed
        // For now, we rely on the message count from the database
    }
}
