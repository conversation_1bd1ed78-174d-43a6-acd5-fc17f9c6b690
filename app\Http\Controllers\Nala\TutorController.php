<?php

namespace App\Http\Controllers\Nala;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class TutorController extends Controller
{
    private $geminiApiKey;
    private $geminiModel;

    public function __construct()
    {
        $this->geminiApiKey = config('services.gemini.api_key', env('GEMINI_API_KEY'));
        $this->geminiModel = config('services.gemini.model', env('GEMINI_MODEL', 'gemini-2.0-flash'));
    }

    /**
     * Handle tutor-related questions - Send data to Gemini AI
     */
    public function handleTutorQuestion($message, $context, $userProfile, $membership, $tutorContext = [])
    {
        try {
            // Enhanced debug logging
            Log::info('Nala TutorController: Received tutor question', [
                'message' => $message,
                'context' => $context,
                'membership' => $membership,
                'tutor_context_keys' => array_keys($tutorContext),
                'tutor_context_full' => $tutorContext,
                'courses_count' => count($tutorContext['courses'] ?? []),
                'exams_count' => count($tutorContext['exams'] ?? []),
                'blogs_count' => count($tutorContext['blogs'] ?? []),
                'tutor_info' => $tutorContext['tutor'] ?? null,
                'stats_info' => $tutorContext['stats'] ?? null
            ]);

            // Build comprehensive tutor data for Gemini
            $tutorData = $this->buildTutorDataForGemini($tutorContext, $context, $userProfile, $membership);

            // Create system prompt with tutor data
            $systemPrompt = $this->buildTutorSystemPrompt($userProfile, $tutorData, $membership);

            // Create user prompt
            $userPrompt = $this->buildTutorUserPrompt($message, $context, $tutorData);

            // Call Gemini AI with tutor data
            return $this->callGeminiAPI($systemPrompt, $userPrompt);

        } catch (\Exception $e) {
            Log::error('Nala TutorController Error: ' . $e->getMessage());
            return $this->getFallbackTutorResponse($message, $tutorContext);
        }
    }

    /**
     * Build tutor data for Gemini AI
     */
    private function buildTutorDataForGemini($tutorContext, $context, $userProfile, $membership)
    {
        $tutorData = [
            'tutor_info' => $tutorContext['tutor'] ?? [],
            'stats' => $tutorContext['stats'] ?? [],
            'courses' => $tutorContext['courses'] ?? [],
            'exams' => $tutorContext['exams'] ?? [],
            'blogs' => $tutorContext['blogs'] ?? [],
            'context' => $context,
            'user_membership' => $membership
        ];

        return $tutorData;
    }

    /**
     * Build system prompt for tutor questions
     */
    private function buildTutorSystemPrompt($userProfile, $tutorData, $membership)
    {
        $userName = $userProfile['basic_info']['name'] ?? 'Pengguna';
        $tutorName = $tutorData['tutor_info']['name'] ?? 'Tutor';
        
        $prompt = "Anda adalah Nala, asisten AI Ngambiskuy yang membantu user memahami profil tutor.

KONTEKS TUTOR:
Nama: {$tutorName}
Posisi: " . ($tutorData['tutor_info']['job_title'] ?? 'Tidak diketahui') . "
Perusahaan: " . ($tutorData['tutor_info']['company'] ?? 'Tidak diketahui') . "
Lokasi: " . ($tutorData['tutor_info']['location'] ?? 'Tidak diketahui') . "
Pendidikan: " . ($tutorData['tutor_info']['education_level'] ?? 'Tidak diketahui') . "
Bergabung: " . ($tutorData['tutor_info']['joined_date'] ?? 'Tidak diketahui') . "

STATISTIK TUTOR:
- Total Kursus: " . ($tutorData['stats']['total_courses'] ?? 0) . "
- Total Ujian: " . ($tutorData['stats']['total_exams'] ?? 0) . "
- Total Siswa: " . ($tutorData['stats']['total_students'] ?? 0) . "
- Rating Rata-rata: " . ($tutorData['stats']['average_rating'] ?? 0) . "

KEAHLIAN TUTOR:";

        if (!empty($tutorData['tutor_info']['skills'])) {
            $skills = is_array($tutorData['tutor_info']['skills']) 
                ? implode(', ', $tutorData['tutor_info']['skills'])
                : $tutorData['tutor_info']['skills'];
            $prompt .= "\n- " . $skills;
        } else {
            $prompt .= "\n- Belum ada informasi keahlian";
        }

        $prompt .= "\n\nDESKRIPSI TUTOR:";
        if (!empty($tutorData['tutor_info']['description'])) {
            $prompt .= "\n" . $tutorData['tutor_info']['description'];
        } else {
            $prompt .= "\nBelum ada deskripsi tutor.";
        }

        // Add courses information
        if (!empty($tutorData['courses']) && is_array($tutorData['courses']) && count($tutorData['courses']) > 0) {
            $prompt .= "\n\nKURSUS YANG DITAWARKAN:";
            foreach (array_slice($tutorData['courses'], 0, 5) as $course) {
                $courseTitle = $course['title'] ?? 'Untitled Course';
                $courseLevel = $course['level'] ?? 'Unknown Level';
                $prompt .= "\n- {$courseTitle} ({$courseLevel})";

                if (!empty($course['description'])) {
                    $description = substr($course['description'], 0, 100);
                    $prompt .= " - {$description}...";
                }

                if (!empty($course['category'])) {
                    $prompt .= " [Kategori: {$course['category']}]";
                }
            }
        } else {
            $prompt .= "\n\nKURSUS YANG DITAWARKAN:";
            $prompt .= "\n- Tutor ini belum memiliki kursus yang dipublikasikan";
        }

        // Add exams information
        if (!empty($tutorData['exams']) && is_array($tutorData['exams']) && count($tutorData['exams']) > 0) {
            $prompt .= "\n\nUJIAN YANG TERSEDIA:";
            foreach (array_slice($tutorData['exams'], 0, 3) as $exam) {
                $examTitle = $exam['title'] ?? 'Untitled Exam';
                $examLevel = $exam['difficulty_level'] ?? 'Unknown Level';
                $prompt .= "\n- {$examTitle} ({$examLevel})";
            }
        } else {
            $prompt .= "\n\nUJIAN YANG TERSEDIA:";
            $prompt .= "\n- Tutor ini belum memiliki ujian yang dipublikasikan";
        }

        $prompt .= "\n\nTUGAS ANDA:
1. Berikan informasi yang akurat tentang tutor berdasarkan data di atas
2. Jelaskan keahlian dan pengalaman tutor dengan detail
3. Rekomendasikan kursus yang sesuai dengan minat user
4. Bantu user memahami mengapa tutor ini cocok untuk mereka
5. Jawab dengan ramah dan natural seperti teman

LARANGAN:
- Jangan buat informasi yang tidak ada dalam data
- Jangan berlebihan dalam memuji
- Jangan sebutkan sistem teknis atau database
- Bicara natural, bukan formal

User: {$userName}
Membership: {$membership}

Jawab dengan ramah dan informatif!";

        return $prompt;
    }

    /**
     * Build user prompt for tutor questions
     */
    private function buildTutorUserPrompt($message, $context, $tutorData)
    {
        $tutorName = $tutorData['tutor_info']['name'] ?? 'Tutor';
        
        $prompt = "Pertanyaan user tentang tutor {$tutorName}: {$message}

Konteks halaman: {$context}

Berikan jawaban yang:
1. Spesifik tentang tutor ini
2. Berdasarkan data yang tersedia
3. Membantu user membuat keputusan
4. Natural dan ramah
5. Maksimal 150 kata";

        return $prompt;
    }

    /**
     * Get fallback response for tutor questions
     */
    private function getFallbackTutorResponse($message, $tutorContext)
    {
        $tutorName = $tutorContext['tutor']['name'] ?? 'tutor ini';
        
        $fallbackResponses = [
            "Saya bisa bantu jelaskan tentang {$tutorName}! Apa yang ingin Anda ketahui lebih lanjut?",
            "Berdasarkan profil {$tutorName}, ada beberapa hal menarik yang bisa saya ceritakan. Mau tahu tentang apa?",
            "Tutor {$tutorName} memiliki pengalaman yang bagus. Ada aspek tertentu yang ingin Anda ketahui?",
            "Saya siap membantu Anda memahami lebih dalam tentang {$tutorName}. Tanya aja!"
        ];

        return $fallbackResponses[array_rand($fallbackResponses)];
    }

    /**
     * Call Gemini AI API
     */
    private function callGeminiAPI($systemPrompt, $userPrompt)
    {
        if (!$this->geminiApiKey) {
            throw new \Exception('Gemini API key not configured');
        }

        $url = "https://generativelanguage.googleapis.com/v1beta/models/{$this->geminiModel}:generateContent?key={$this->geminiApiKey}";

        $payload = [
            'contents' => [
                [
                    'parts' => [
                        ['text' => $systemPrompt . "\n\n" . $userPrompt]
                    ]
                ]
            ],
            'generationConfig' => [
                'temperature' => 0.8,
                'topK' => 40,
                'topP' => 0.95,
                'maxOutputTokens' => 400,
            ]
        ];

        $response = Http::timeout(20)->post($url, $payload);

        if (!$response->successful()) {
            throw new \Exception('Gemini API request failed: ' . $response->body());
        }

        $data = $response->json();

        if (!isset($data['candidates'][0]['content']['parts'][0]['text'])) {
            throw new \Exception('Invalid response format from Gemini API');
        }

        return trim($data['candidates'][0]['content']['parts'][0]['text']);
    }
}
