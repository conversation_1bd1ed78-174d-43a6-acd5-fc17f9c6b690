

<?php $__env->startSection('title', $profile->public_name . ' - Tutor Profesional | Ngambiskuy'); ?>

<?php $__env->startSection('meta'); ?>
<meta name="description" content="<?php echo e($profile->description ?? 'Tutor profesional di Ngambiskuy dengan pengalaman mengajar ' . $achievements['years_teaching'] . ' tahun. Bergabung dengan ' . number_format($stats['total_students']) . ' siswa yang telah belajar.'); ?>">
<meta name="keywords" content="tutor, <?php echo e($profile->public_name); ?>, ngambiskuy, kursus online, pembelajaran">
<meta property="og:title" content="<?php echo e($profile->public_name); ?> - Tutor Profesional | Ngambiskuy">
<meta property="og:description" content="<?php echo e($profile->description ?? 'Tutor profesional dengan ' . $achievements['years_teaching'] . ' tahun pengalaman mengajar.'); ?>">
<meta property="og:image" content="<?php echo e($profile->user->getProfilePictureUrl()); ?>">
<meta property="og:url" content="<?php echo e(url()->current()); ?>">
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<!-- Hero Section -->
<div class="bg-gradient-to-br from-primary to-primary-dark text-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 items-center">
            <!-- Tutor Info -->
            <div class="lg:col-span-2">
                <div class="flex flex-col md:flex-row items-start md:items-center space-y-6 md:space-y-0 md:space-x-8">
                    <!-- Profile Picture -->
                    <div class="relative">
                        <div class="w-32 h-32 rounded-full overflow-hidden border-4 border-white/20 shadow-xl">
                            <img src="<?php echo e($profile->user->getProfilePictureUrl()); ?>"
                                 alt="<?php echo e($profile->public_name); ?>"
                                 class="w-full h-full object-cover">
                        </div>
                        <!-- Verified Badge -->
                        <div class="absolute -bottom-2 -right-2 bg-green-500 rounded-full p-2 border-4 border-white">
                            <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                    </div>

                    <!-- Tutor Details -->
                    <div class="flex-1">
                        <div class="flex flex-col md:flex-row md:items-center md:space-x-4 mb-4">
                            <h1 class="text-4xl font-bold mb-2 md:mb-0"><?php echo e($profile->public_name); ?></h1>
                            <?php if($profile->user->job_title): ?>
                                <span class="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-white/20 text-white">
                                    <?php echo e($profile->user->job_title); ?>

                                </span>
                            <?php endif; ?>
                        </div>

                        <div class="flex flex-wrap items-center gap-4 mb-4 text-white/90">
                            <div class="flex items-center">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l9-5-9-5-9 5 9 5z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z"></path>
                                </svg>
                                <?php echo e($profile->education_level); ?>

                            </div>
                            <?php if($profile->user->location): ?>
                                <div class="flex items-center">
                                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    </svg>
                                    <?php echo e($profile->user->location); ?>

                                </div>
                            <?php endif; ?>
                            <div class="flex items-center">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 0h6m-6 0l-2 2m8-2l2 2m-2-2v6a2 2 0 01-2 2H8a2 2 0 01-2-2v-6"></path>
                                </svg>
                                <?php echo e($achievements['years_teaching']); ?> tahun mengajar
                            </div>
                        </div>

                        <?php if($profile->description): ?>
                            <p class="text-lg text-white/90 leading-relaxed mb-6"><?php echo e($profile->description); ?></p>
                        <?php endif; ?>

                        <!-- Specialties -->
                        <?php if($specialties->count() > 0): ?>
                            <div class="flex flex-wrap gap-2 mb-6">
                                <?php $__currentLoopData = $specialties; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $specialty): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-white/20 text-white">
                                        <?php echo e($specialty->name); ?>

                                    </span>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Achievement Stats -->
            <div class="lg:col-span-1">
                <div class="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20">
                    <h3 class="text-xl font-bold text-white mb-6">Pencapaian</h3>
                    <div class="space-y-4">
                        <div class="text-center">
                            <div class="text-3xl font-bold text-white"><?php echo e(number_format($stats['total_students'])); ?></div>
                            <div class="text-white/80 text-sm">Total Siswa</div>
                        </div>
                        <div class="text-center">
                            <div class="text-3xl font-bold text-white"><?php echo e($achievements['total_content']); ?></div>
                            <div class="text-white/80 text-sm">Konten Tersedia</div>
                        </div>
                        <div class="text-center">
                            <div class="text-3xl font-bold text-white"><?php echo e(number_format($stats['average_rating'], 1)); ?></div>
                            <div class="text-white/80 text-sm">Rating Rata-rata</div>
                        </div>
                        <div class="text-center">
                            <div class="text-3xl font-bold text-white"><?php echo e($achievements['years_teaching']); ?></div>
                            <div class="text-white/80 text-sm">Tahun Mengajar</div>
                        </div>
                    </div>

                    <!-- Contact Button -->
                    <div class="mt-6">
                        <a href="https://wa.me/<?php echo e(preg_replace('/[^0-9]/', '', $profile->phone_number)); ?>"
                           target="_blank"
                           class="w-full bg-white text-primary hover:bg-gray-50 font-semibold py-3 px-6 rounded-lg transition-colors flex items-center justify-center">
                            <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"/>
                            </svg>
                            Hubungi Tutor
                        </a>
                        <p class="text-white/70 text-xs text-center mt-2">
                            Bergabung sejak <?php echo e($profile->created_at->format('M Y')); ?>

                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Main Content -->
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
    <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
        <!-- Main Content Area -->
        <div class="lg:col-span-3">
            <!-- Content Navigation -->
            <div class="bg-white rounded-lg shadow-sm border mb-8">
                <div class="border-b border-gray-200">
                    <nav class="flex space-x-8 px-8 pt-6" aria-label="Tabs">
                        <button onclick="showTab('courses')" id="courses-tab" class="tab-button active whitespace-nowrap pb-4 px-1 border-b-2 font-medium text-sm">
                            <div class="flex items-center">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                                </svg>
                                Kursus (<?php echo e($stats['total_courses']); ?>)
                            </div>
                        </button>
                        <button onclick="showTab('exams')" id="exams-tab" class="tab-button whitespace-nowrap pb-4 px-1 border-b-2 font-medium text-sm">
                            <div class="flex items-center">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                                Ujian (<?php echo e($stats['total_exams']); ?>)
                            </div>
                        </button>
                        <button onclick="showTab('blogs')" id="blogs-tab" class="tab-button whitespace-nowrap pb-4 px-1 border-b-2 font-medium text-sm">
                            <div class="flex items-center">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                </svg>
                                Blog (<?php echo e($stats['total_blog_posts']); ?>)
                            </div>
                        </button>
                        <button onclick="showTab('about')" id="about-tab" class="tab-button whitespace-nowrap pb-4 px-1 border-b-2 font-medium text-sm">
                            <div class="flex items-center">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                </svg>
                                Tentang
                            </div>
                        </button>
                    </nav>
                </div>

                <!-- Courses Tab -->
                <div id="courses-content" class="tab-content p-8">
                    <?php if($courses->count() > 0): ?>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <?php $__currentLoopData = $courses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $course): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="bg-white border border-gray-200 rounded-xl overflow-hidden hover:shadow-lg transition-all duration-300 group">
                                    <div class="aspect-video bg-gray-100 relative overflow-hidden">
                                        <?php if($course->thumbnail): ?>
                                            <img src="<?php echo e(asset('storage/' . $course->thumbnail)); ?>"
                                                 alt="<?php echo e($course->title); ?>"
                                                 class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300">
                                        <?php else: ?>
                                            <div class="w-full h-full flex items-center justify-center bg-gradient-to-br from-primary/10 to-primary/20">
                                                <svg class="w-16 h-16 text-primary/40" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                                                </svg>
                                            </div>
                                        <?php endif; ?>

                                        <!-- Price Badge -->
                                        <div class="absolute top-4 right-4">
                                            <?php if($course->is_free): ?>
                                                <span class="bg-green-500 text-white px-3 py-1 rounded-full text-sm font-semibold shadow-lg">Gratis</span>
                                            <?php else: ?>
                                                <span class="bg-primary text-white px-3 py-1 rounded-full text-sm font-semibold shadow-lg">Rp <?php echo e(number_format($course->price, 0, ',', '.')); ?></span>
                                            <?php endif; ?>
                                        </div>

                                        <!-- Level Badge -->
                                        <div class="absolute top-4 left-4">
                                            <span class="bg-black/70 text-white px-3 py-1 rounded-full text-xs font-medium"><?php echo e(ucfirst($course->level)); ?></span>
                                        </div>
                                    </div>

                                    <div class="p-6">
                                        <?php if($course->category): ?>
                                            <div class="flex items-center mb-3">
                                                <span class="bg-primary/10 text-primary px-3 py-1 rounded-full text-xs font-medium"><?php echo e($course->category->name); ?></span>
                                            </div>
                                        <?php endif; ?>

                                        <h3 class="font-bold text-gray-900 mb-3 line-clamp-2 text-lg group-hover:text-primary transition-colors"><?php echo e($course->title); ?></h3>
                                        <p class="text-gray-600 text-sm mb-4 line-clamp-2"><?php echo e($course->description); ?></p>

                                        <div class="flex items-center justify-between text-sm text-gray-500 mb-4">
                                            <div class="flex items-center">
                                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                </svg>
                                                <?php echo e($course->total_duration_minutes); ?> menit
                                            </div>
                                            <div class="flex items-center">
                                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                                                </svg>
                                                <?php echo e($course->total_lessons); ?> pelajaran
                                            </div>
                                        </div>

                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center">
                                                <div class="flex text-yellow-400 mr-2">
                                                    <?php for($i = 1; $i <= 5; $i++): ?>
                                                        <?php if($i <= floor($course->average_rating)): ?>
                                                            <svg class="w-4 h-4 fill-current" viewBox="0 0 20 20">
                                                                <path d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z"/>
                                                            </svg>
                                                        <?php else: ?>
                                                            <svg class="w-4 h-4 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                                                                <path d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z"/>
                                                            </svg>
                                                        <?php endif; ?>
                                                    <?php endfor; ?>
                                                </div>
                                                <span class="text-sm font-medium text-gray-900"><?php echo e(number_format($course->average_rating, 1)); ?></span>
                                                <span class="text-sm text-gray-500 ml-1">(<?php echo e(number_format($course->total_students)); ?>)</span>
                                            </div>

                                            <a href="<?php echo e(route('course.show', $course)); ?>" class="text-primary hover:text-primary-dark font-semibold text-sm transition-colors">
                                                Lihat Detail →
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>

                        <?php if($stats['total_courses'] > 6): ?>
                            <div class="text-center mt-8">
                                <a href="<?php echo e(route('courses.index')); ?>?tutor=<?php echo e($profile->user->id); ?>" class="btn btn-outline btn-lg">
                                    Lihat Semua Kursus (<?php echo e($stats['total_courses']); ?>)
                                </a>
                            </div>
                        <?php endif; ?>
                    <?php else: ?>
                        <div class="text-center py-16">
                            <svg class="w-20 h-20 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                            </svg>
                            <h3 class="text-xl font-semibold text-gray-900 mb-2">Belum Ada Kursus</h3>
                            <p class="text-gray-500"><?php echo e($profile->public_name); ?> belum membuat kursus. Pantau terus untuk update terbaru!</p>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Exams Tab -->
                <div id="exams-content" class="tab-content p-8 hidden">
                    <?php if($exams->count() > 0): ?>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <?php $__currentLoopData = $exams; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $exam): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="bg-white border border-gray-200 rounded-xl overflow-hidden hover:shadow-lg transition-all duration-300 group">
                                    <div class="p-6">
                                        <div class="flex items-center justify-between mb-4">
                                            <?php if($exam->category): ?>
                                                <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-xs font-medium"><?php echo e($exam->category->name); ?></span>
                                            <?php endif; ?>
                                            <span class="bg-<?php echo e($exam->difficulty_level === 'beginner' ? 'green' : ($exam->difficulty_level === 'intermediate' ? 'yellow' : 'red')); ?>-100 text-<?php echo e($exam->difficulty_level === 'beginner' ? 'green' : ($exam->difficulty_level === 'intermediate' ? 'yellow' : 'red')); ?>-800 px-3 py-1 rounded-full text-xs font-medium">
                                                <?php echo e(ucfirst($exam->difficulty_level)); ?>

                                            </span>
                                        </div>

                                        <h3 class="font-bold text-gray-900 mb-3 line-clamp-2 text-lg group-hover:text-primary transition-colors"><?php echo e($exam->title); ?></h3>
                                        <p class="text-gray-600 text-sm mb-4 line-clamp-3"><?php echo e($exam->description); ?></p>

                                        <div class="space-y-2 mb-4">
                                            <div class="flex items-center text-sm text-gray-500">
                                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                </svg>
                                                <?php echo e($exam->time_limit); ?> menit
                                            </div>
                                            <div class="flex items-center text-sm text-gray-500">
                                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                                </svg>
                                                <?php echo e($exam->total_questions); ?> soal
                                            </div>
                                            <div class="flex items-center text-sm text-gray-500">
                                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                </svg>
                                                Passing score: <?php echo e($exam->passing_score); ?>%
                                            </div>
                                        </div>

                                        <div class="flex items-center justify-between">
                                            <div class="text-lg font-bold text-primary">
                                                <?php if($exam->price > 0): ?>
                                                    Rp <?php echo e(number_format($exam->price, 0, ',', '.')); ?>

                                                <?php else: ?>
                                                    Gratis
                                                <?php endif; ?>
                                            </div>

                                            <a href="<?php echo e(route('exams.show', $exam)); ?>" class="text-primary hover:text-primary-dark font-semibold text-sm transition-colors">
                                                Lihat Detail →
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>

                        <?php if($stats['total_exams'] > 6): ?>
                            <div class="text-center mt-8">
                                <a href="<?php echo e(route('exams.index')); ?>?tutor=<?php echo e($profile->user->id); ?>" class="btn btn-outline btn-lg">
                                    Lihat Semua Ujian (<?php echo e($stats['total_exams']); ?>)
                                </a>
                            </div>
                        <?php endif; ?>
                    <?php else: ?>
                        <div class="text-center py-16">
                            <svg class="w-20 h-20 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                            <h3 class="text-xl font-semibold text-gray-900 mb-2">Belum Ada Ujian</h3>
                            <p class="text-gray-500"><?php echo e($profile->public_name); ?> belum membuat ujian. Pantau terus untuk update terbaru!</p>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Blogs Tab -->
                <div id="blogs-content" class="tab-content p-8 hidden">
                    <?php if($blogPosts->count() > 0): ?>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <?php $__currentLoopData = $blogPosts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $post): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <article class="bg-white border border-gray-200 rounded-xl overflow-hidden hover:shadow-lg transition-all duration-300 group">
                                    <?php if($post->featured_image): ?>
                                        <div class="aspect-video bg-gray-100 overflow-hidden">
                                            <img src="<?php echo e(asset('storage/' . $post->featured_image)); ?>"
                                                 alt="<?php echo e($post->title); ?>"
                                                 class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300">
                                        </div>
                                    <?php endif; ?>

                                    <div class="p-6">
                                        <div class="flex items-center text-sm text-gray-500 mb-3">
                                            <?php if($post->category): ?>
                                                <span class="bg-gray-100 text-gray-800 px-3 py-1 rounded-full text-xs mr-3"><?php echo e($post->category->name); ?></span>
                                            <?php endif; ?>
                                            <time datetime="<?php echo e($post->published_at->format('Y-m-d')); ?>">
                                                <?php echo e($post->published_at->format('d M Y')); ?>

                                            </time>
                                            <span class="mx-2">•</span>
                                            <span><?php echo e($post->read_time); ?> menit baca</span>
                                        </div>

                                        <h3 class="font-bold text-gray-900 mb-3 line-clamp-2 text-lg group-hover:text-primary transition-colors">
                                            <a href="<?php echo e(route('blog.show', $post)); ?>">
                                                <?php echo e($post->title); ?>

                                            </a>
                                        </h3>

                                        <p class="text-gray-600 text-sm mb-4 line-clamp-3"><?php echo e($post->excerpt); ?></p>

                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center text-sm text-gray-500">
                                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                                </svg>
                                                <?php echo e($post->views_count); ?> views
                                            </div>

                                            <a href="<?php echo e(route('blog.show', $post)); ?>" class="text-primary hover:text-primary-dark font-semibold text-sm transition-colors">
                                                Baca Selengkapnya →
                                            </a>
                                        </div>
                                    </div>
                                </article>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>

                        <?php if($stats['total_blog_posts'] > 6): ?>
                            <div class="text-center mt-8">
                                <a href="<?php echo e(route('blog.index')); ?>?author=<?php echo e($profile->user->id); ?>" class="btn btn-outline btn-lg">
                                    Lihat Semua Blog (<?php echo e($stats['total_blog_posts']); ?>)
                                </a>
                            </div>
                        <?php endif; ?>
                    <?php else: ?>
                        <div class="text-center py-16">
                            <svg class="w-20 h-20 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                            </svg>
                            <h3 class="text-xl font-semibold text-gray-900 mb-2">Belum Ada Blog</h3>
                            <p class="text-gray-500"><?php echo e($profile->public_name); ?> belum menulis blog. Pantau terus untuk update terbaru!</p>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- About Tab -->
                <div id="about-content" class="tab-content p-8 hidden">
                    <?php if($profile->long_description || $profile->description): ?>
                        <div class="prose prose-gray max-w-none">
                            <?php if($profile->long_description): ?>
                                <div class="text-gray-700 leading-relaxed whitespace-pre-line"><?php echo e($profile->long_description); ?></div>
                            <?php else: ?>
                                <p class="text-gray-700 leading-relaxed"><?php echo e($profile->description); ?></p>
                            <?php endif; ?>
                        </div>

                        <!-- Education Info -->
                        <div class="mt-8 pt-8 border-t">
                            <h3 class="text-xl font-bold text-gray-900 mb-4">Latar Belakang Pendidikan</h3>
                            <div class="flex items-center space-x-3">
                                <div class="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center">
                                    <svg class="w-6 h-6 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l9-5-9-5-9 5 9 5z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <div class="font-semibold text-gray-900"><?php echo e($profile->education_level); ?></div>
                                    <div class="text-gray-600 text-sm">Tingkat Pendidikan</div>
                                </div>
                            </div>
                        </div>

                        <!-- Skills -->
                        <?php if($profile->user->getSkillsArray()): ?>
                            <div class="mt-8 pt-8 border-t">
                                <h3 class="text-xl font-bold text-gray-900 mb-4">Keahlian</h3>
                                <div class="flex flex-wrap gap-3">
                                    <?php $__currentLoopData = $profile->user->getSkillsArray(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $skill): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <span class="inline-flex items-center px-4 py-2 rounded-lg text-sm font-medium bg-primary/10 text-primary border border-primary/20">
                                            <?php echo e($skill); ?>

                                        </span>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                            </div>
                        <?php endif; ?>
                    <?php else: ?>
                        <div class="text-center py-16">
                            <svg class="w-20 h-20 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                            </svg>
                            <h3 class="text-xl font-semibold text-gray-900 mb-2">Informasi Belum Tersedia</h3>
                            <p class="text-gray-500"><?php echo e($profile->public_name); ?> belum menambahkan informasi detail tentang dirinya.</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="lg:col-span-1">
            <div class="space-y-6">
                <!-- Quick Stats -->
                <div class="bg-white rounded-lg shadow-sm border p-6">
                    <h3 class="text-lg font-bold text-gray-900 mb-4">Statistik Tutor</h3>
                    <div class="space-y-4">
                        <div class="flex items-center justify-between">
                            <span class="text-gray-600">Total Kursus</span>
                            <span class="font-bold text-primary"><?php echo e($stats['total_courses']); ?></span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-gray-600">Total Ujian</span>
                            <span class="font-bold text-primary"><?php echo e($stats['total_exams']); ?></span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-gray-600">Total Siswa</span>
                            <span class="font-bold text-primary"><?php echo e(number_format($stats['total_students'])); ?></span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-gray-600">Rating</span>
                            <div class="flex items-center">
                                <span class="font-bold text-primary mr-1"><?php echo e(number_format($stats['average_rating'], 1)); ?></span>
                                <div class="flex text-yellow-400">
                                    <?php for($i = 1; $i <= 5; $i++): ?>
                                        <?php if($i <= floor($stats['average_rating'])): ?>
                                            <svg class="w-4 h-4 fill-current" viewBox="0 0 20 20">
                                                <path d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z"/>
                                            </svg>
                                        <?php else: ?>
                                            <svg class="w-4 h-4 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                                                <path d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z"/>
                                            </svg>
                                        <?php endif; ?>
                                    <?php endfor; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Reviews -->
                <?php if($recentReviews->count() > 0): ?>
                    <div class="bg-white rounded-lg shadow-sm border p-6">
                        <h3 class="text-lg font-bold text-gray-900 mb-4">Ulasan Terbaru</h3>
                        <div class="space-y-4">
                            <?php $__currentLoopData = $recentReviews; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $review): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="border-b border-gray-100 last:border-b-0 pb-4 last:pb-0">
                                    <div class="flex items-center mb-2">
                                        <div class="flex text-yellow-400 mr-2">
                                            <?php for($i = 1; $i <= $review->rating; $i++): ?>
                                                <svg class="w-3 h-3 fill-current" viewBox="0 0 20 20">
                                                    <path d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z"/>
                                                </svg>
                                            <?php endfor; ?>
                                        </div>
                                        <span class="text-sm font-medium text-gray-900"><?php echo e($review->user->name); ?></span>
                                    </div>
                                    <p class="text-sm text-gray-600 line-clamp-2"><?php echo e($review->comment); ?></p>
                                    <p class="text-xs text-gray-500 mt-1"><?php echo e($review->created_at->diffForHumans()); ?></p>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Share Profile -->
                <div class="bg-white rounded-lg shadow-sm border p-6">
                    <h3 class="text-lg font-bold text-gray-900 mb-4">Bagikan Profil</h3>
                    <div class="space-y-3">
                        <button onclick="shareToWhatsApp()" class="w-full btn btn-outline text-sm py-2">
                            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"/>
                            </svg>
                            WhatsApp
                        </button>
                        <button onclick="shareToTwitter()" class="w-full btn btn-outline text-sm py-2">
                            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                            </svg>
                            Twitter
                        </button>
                        <button onclick="copyProfileLink()" class="w-full btn btn-outline text-sm py-2">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                            </svg>
                            Salin Link
                        </button>
                    </div>
                </div>

                <!-- Back to Home -->
                <div class="text-center">
                    <a href="<?php echo e(route('home')); ?>" class="text-primary hover:text-primary-dark font-medium text-sm">
                        ← Kembali ke Beranda
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
    .tab-button {
        @apply border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 transition-colors;
    }

    .tab-button.active {
        @apply border-primary text-primary;
    }

    .line-clamp-2 {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    .line-clamp-3 {
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    .aspect-video {
        aspect-ratio: 16 / 9;
    }

    /* Gradient background animation */
    .bg-gradient-to-br {
        background-size: 400% 400%;
        animation: gradientShift 15s ease infinite;
    }

    @keyframes gradientShift {
        0% { background-position: 0% 50%; }
        50% { background-position: 100% 50%; }
        100% { background-position: 0% 50%; }
    }

    /* Hover effects */
    .group:hover .group-hover\:scale-105 {
        transform: scale(1.05);
    }

    /* Backdrop blur support */
    .backdrop-blur-sm {
        backdrop-filter: blur(4px);
    }

    /* Custom scrollbar for better UX */
    ::-webkit-scrollbar {
        width: 6px;
    }

    ::-webkit-scrollbar-track {
        background: #f1f1f1;
    }

    ::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 3px;
    }

    ::-webkit-scrollbar-thumb:hover {
        background: #a8a8a8;
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
function showTab(tabName) {
    // Hide all tab contents
    const tabContents = document.querySelectorAll('.tab-content');
    tabContents.forEach(content => {
        content.classList.add('hidden');
    });

    // Remove active class from all tab buttons
    const tabButtons = document.querySelectorAll('.tab-button');
    tabButtons.forEach(button => {
        button.classList.remove('active');
    });

    // Show selected tab content
    const selectedContent = document.getElementById(tabName + '-content');
    if (selectedContent) {
        selectedContent.classList.remove('hidden');
    }

    // Add active class to selected tab button
    const selectedButton = document.getElementById(tabName + '-tab');
    if (selectedButton) {
        selectedButton.classList.add('active');
    }
}

function shareToWhatsApp() {
    const profileUrl = window.location.href;
    const tutorName = '<?php echo e($profile->public_name); ?>';
    const message = `🎓 Lihat profil tutor ${tutorName} di Ngambiskuy! Tutor profesional dengan ${tutorName} tahun pengalaman mengajar. #Ngambiskuy #OnlineLearning`;

    const whatsappUrl = `https://wa.me/?text=${encodeURIComponent(message + ' ' + profileUrl)}`;
    window.open(whatsappUrl, '_blank');
}

function shareToTwitter() {
    const profileUrl = window.location.href;
    const tutorName = '<?php echo e($profile->public_name); ?>';
    const message = `🎓 Belajar dari tutor profesional ${tutorName} di @Ngambiskuy! ✨ Pengalaman mengajar <?php echo e($achievements['years_teaching']); ?> tahun dengan <?php echo e(number_format($stats['total_students'])); ?> siswa. #OnlineLearning #Tutor`;

    const twitterUrl = `https://twitter.com/intent/tweet?text=${encodeURIComponent(message)}&url=${encodeURIComponent(profileUrl)}`;
    window.open(twitterUrl, '_blank');
}

function copyProfileLink() {
    const profileUrl = window.location.href;

    if (navigator.clipboard) {
        navigator.clipboard.writeText(profileUrl).then(() => {
            showNotification('Link profil berhasil disalin!', 'success');
        }).catch(() => {
            fallbackCopyTextToClipboard(profileUrl);
        });
    } else {
        fallbackCopyTextToClipboard(profileUrl);
    }
}

function fallbackCopyTextToClipboard(text) {
    const textArea = document.createElement("textarea");
    textArea.value = text;
    textArea.style.top = "0";
    textArea.style.left = "0";
    textArea.style.position = "fixed";

    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();

    try {
        document.execCommand('copy');
        showNotification('Link profil berhasil disalin!', 'success');
    } catch (err) {
        showNotification('Gagal menyalin link. Silakan salin manual.', 'error');
    }

    document.body.removeChild(textArea);
}

function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 px-6 py-3 rounded-lg shadow-lg text-white font-medium transition-all duration-300 transform translate-x-full`;

    if (type === 'success') {
        notification.classList.add('bg-green-500');
    } else if (type === 'error') {
        notification.classList.add('bg-red-500');
    } else {
        notification.classList.add('bg-blue-500');
    }

    notification.textContent = message;
    document.body.appendChild(notification);

    // Animate in
    setTimeout(() => {
        notification.classList.remove('translate-x-full');
    }, 100);

    // Animate out and remove
    setTimeout(() => {
        notification.classList.add('translate-x-full');
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// Initialize the first tab as active on page load
document.addEventListener('DOMContentLoaded', function() {
    showTab('courses');

    // Add smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
});

// Add intersection observer for animations
const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
};

const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            entry.target.classList.add('animate-fade-in');
        }
    });
}, observerOptions);

// Observe all cards for animation
document.addEventListener('DOMContentLoaded', function() {
    const cards = document.querySelectorAll('.group');
    cards.forEach(card => observer.observe(card));
});
</script>
<?php $__env->stopPush(); ?>
<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\bps renata kerja\2024\project stat sektoral website\Taylor-Swift-Web-Project-main\ngambiskuynew\resources\views/tutor/public-profile-v2.blade.php ENDPATH**/ ?>